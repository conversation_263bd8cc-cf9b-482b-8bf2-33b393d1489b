{"name": "demo.appio.so", "scripts": {"clean": "rm -rf ./dist/*", "minify:js": "find src -type f -name '*.js' -exec sh -c 'for f in \"$@\"; do out=dist/${f#src/}; mkdir -p \"$(dirname \"$out\")\"; npx terser \"$f\" --compress --mangle --output \"$out\"; done' sh {} +", "minify:css": "find src -type f -name '*.css' -exec sh -c 'for f in \"$@\"; do out=dist/${f#src/}; mkdir -p \"$(dirname \"$out\")\"; cleancss \"$f\" -o \"$out\"; done' sh {} +", "minify:html": "find src -type f -name '*.html' -exec sh -c 'for f in \"$@\"; do out=dist/${f#src/}; mkdir -p \"$(dirname \"$out\")\"; npx html-minifier-terser --collapse-whitespace --remove-comments --minify-css true --minify-js true \"$f\" -o \"$out\"; done' sh {} +", "minify": "npm run minify:js && npm run minify:css && npm run minify:html", "lint:js": "if [ $(find src -type f -name '*.js' | wc -l) -gt 0 ]; then npx eslint \"src/**/*.js\" --max-warnings=0; else echo \"No JS files to lint\"; fi", "lint:css": "if [ $(find src -type f -name '*.css' | wc -l) -gt 0 ]; then npx stylelint \"src/**/*.css\"; else echo \"No CSS files to lint\"; fi", "lint": "npm run lint:js && npm run lint:css", "assets": "rsync -av --include '*/' --include 'robots.txt' --include '*.ico' --include '*.jpg' --include '*.jpeg' --include '*.png' --include '*.gif' --include '*.svg' --exclude '*' ./src/ ./dist/", "inline": "python3 inline.py", "start:src": "http-server ./src --port 1001 -o", "start:dist": "http-server ./dist --port 2001 -o", "start": "npm run start:dist"}, "devDependencies": {"clean-css-cli": "^5.6.3", "eslint": "^9.14.0", "globals": "^15.12.0", "html-minifier-terser": "^7.2.0", "http-server": "^14.1.1", "stylelint": "^16.11.0", "stylelint-config-standard": "^36.0.1", "terser": "^5.36.0"}}