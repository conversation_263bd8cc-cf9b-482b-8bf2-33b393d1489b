#!/bin/sh

# Check if at least one argument was provided
if [ "$#" -eq 0 ]; then
  echo "🔴 Missing commit message"
  exit 1
fi

commit_message="$1"

if [ -z "$commit_message" ]; then
  echo "🔴 Commit message is empty"
  exit 1
fi

# Run ESLint on the source files (you can specify a specific directory or file)
npm run lint

# Capture the exit status of ESLint
if [ $? -ne 0 ]; then
  echo "🔴 ESLint found issues in your code. Please fix them before pushing."
  exit 1
fi

# Clean dist folder
npm run clean

# Run minification - also runs on GitHub action
npm run minify

# Capture the exit status
if [ $? -ne 0 ]; then
  echo "🔴 Minification caused issues. Please fix them before pushing."
  exit 1
fi

# Copy assets from src/ -> dist/
npm run assets

# Inline JavaScripts
npm run inline

# Fix API url
find dist -type f -name "*.html" -exec sed -i '' "s|http://localhost:8081||g" {} +
find dist -type f -name "*.html" -exec sed -i '' "s|http://127.0.0.1:8081||g" {} +

# If all passes, push to git
git add -A
git commit -m"$commit_message"
git push

echo "🟢 Deployed successfully"