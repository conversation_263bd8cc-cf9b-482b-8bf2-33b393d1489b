Frontend for demo.appio.so
==========================

## Local

`make start` starts the src version on http://localhost:1111/

`make test` minifies js,css and starts the dist version on http://localhost:2222/

## Deploy

Locally `./deploy.sh "git commit message"`

Removes local urls `http://localhost:8081/data` for production.

Inlines javascript marked as `<script inline="true" src="..."></script>` via `npm run inline` that uses `inline.py`which python

## Release

On server `make release`