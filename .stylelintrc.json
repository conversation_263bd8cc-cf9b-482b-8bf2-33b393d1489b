{"extends": "stylelint-config-standard", "rules": {"media-feature-range-notation": "prefix", "selector-not-notation": "simple", "selector-pseudo-element-colon-notation": "double", "function-url-quotes": "always", "declaration-block-no-duplicate-properties": true, "property-no-unknown": true, "selector-pseudo-class-no-unknown": true, "selector-pseudo-element-no-unknown": true, "at-rule-no-unknown": true, "color-function-notation": "legacy", "color-no-invalid-hex": true, "no-empty-source": true, "block-no-empty": true, "property-no-vendor-prefix": null, "no-descending-specificity": null, "number-max-precision": 10, "custom-property-pattern": null, "alpha-value-notation": "number", "comment-empty-line-before": null, "rule-empty-line-before": null}}