demo.appio.so {
        root * /var/www/demo.appio.so/fe/dist

        # handled by backend API
        handle /data/* {
            reverse_proxy localhost:8081
        }

        @clear {
            query clear=*
        }
        header @clear {
            Set-Cookie "service_id=; Domain=demo.appio.so; Path=/; Max-Age=0; SameSite=Strict; Secure"
            Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0"
            Pragma "no-cache"
            Expires "0"
        }

        file_server
        encode zstd gzip
        header -Server # Remove the Server header

        handle_errors {
                rewrite * /404.html
                file_server
        }

        # tls /etc/caddy/certs/cloudflare.crt /etc/caddy/certs/cloudflare.key
        log {
                output file /var/log/caddy/demo.appio.so-access.log
        }
        log {
                level ERROR
                output file /var/log/caddy/demo.appio.so-error.log
        }
}