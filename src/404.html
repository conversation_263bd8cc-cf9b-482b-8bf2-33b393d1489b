<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Page not found</title>
    <link rel="icon" id="favicon" type="image/x-icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
    <link rel="stylesheet" href="https://cdn.appio.so/appio.css">
    <style>
        body {
            padding: 3em;
        }
    </style>

    <!-- Amplitude -->
    <script src="https://cdn.amplitude.com/libs/analytics-browser-2.11.1-min.js.gz"></script>
    <script src="https://cdn.amplitude.com/libs/plugin-session-replay-browser-1.8.0-min.js.gz"></script>
    <script>
        if (window.amplitude && typeof window.amplitude.add === "function" && typeof window.amplitude.init === "function") {
            window.amplitude.add(window.sessionReplay.plugin({
                sampleRate: 1
            }));
            window.amplitude.init('422a7294588e2ed56224dc2050be87ca', {
                "autocapture": {
                    "elementInteractions": true
                }
            });
        }
    </script>
</head>
<body>
    <h1>Page not found</h1>
    <p><a href="/">Return to homepage</a></p>

<!--    &lt;!&ndash; PostHog &ndash;&gt;-->
<!--    <script>-->
<!--        !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init me ws ys ps bs capture je Di ks register register_once register_for_session unregister unregister_for_session Ps getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Es $s createPersonProfile Is opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Ss debug xs getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);-->
<!--        posthog.init('phc_LBWGGE289NOf8baQoCIYP9l38yODewgjI2rZnkAfYYi', {-->
<!--            api_host:'https://eu.i.posthog.com',-->
<!--            person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well-->
<!--            autocapture: true,-->
<!--            capture_pageview: true,-->
<!--            cookie_domain: '.appio.so'-->
<!--        })-->
<!--    </script>-->
</body>
</html>
