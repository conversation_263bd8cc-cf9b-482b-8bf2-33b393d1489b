!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(r.ULID={})}(this,(function(r){"use strict";function e(r){var e=new Error(r);return e.source="ulid",e}var t="0123456789ABCDEFGHJKMNPQRSTVWXYZ",n=t.length,o=Math.pow(2,48)-1;function i(r,e,t){return e>r.length-1?r:r.substr(0,e)+t+r.substr(e+1)}function u(r){for(var o=void 0,u=r.length,a=void 0,c=void 0,f=n-1;!o&&u-- >=0;){if(a=r[u],-1===(c=t.indexOf(a)))throw e("incorrectly encoded string");c!==f?o=i(r,u,t[c+1]):r=i(r,u,t[0])}if("string"==typeof o)return o;throw e("cannot increment this string")}function a(r){var e=Math.floor(r()*n);return e===n&&(e=n-1),t.charAt(e)}function c(r,i){if(isNaN(r))throw new Error(r+" must be a number");if(r>o)throw e("cannot encode time greater than "+o);if(r<0)throw e("time must be positive");if(!1===Number.isInteger(r))throw e("time must be an integer");for(var u=void 0,a="";i>0;i--)u=r%n,a=t.charAt(u)+a,r=(r-u)/n;return a}function f(r,e){for(var t="";r>0;r--)t=a(e)+t;return t}function d(){var r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments[1];t||(t="undefined"!=typeof window?window:null);var n=t&&(t.crypto||t.msCrypto);if(n)return function(){var r=new Uint8Array(1);return n.getRandomValues(r),r[0]/255};try{var o=require("crypto");return function(){return o.randomBytes(1).readUInt8()/255}}catch(r){}if(r){try{console.error("secure crypto unusable, falling back to insecure Math.random()!")}catch(r){}return function(){return Math.random()}}throw e("secure crypto unusable, insecure Math.random not allowed")}function s(r){return r||(r=d()),function(e){return isNaN(e)&&(e=Date.now()),c(e,10)+f(16,r)}}var h=s();r.replaceCharAt=i,r.incrementBase32=u,r.randomChar=a,r.encodeTime=c,r.encodeRandom=f,r.decodeTime=function(r){if(26!==r.length)throw e("malformed ulid");var i=r.substr(0,10).split("").reverse().reduce((function(r,o,i){var u=t.indexOf(o);if(-1===u)throw e("invalid character found: "+o);return r+u*Math.pow(n,i)}),0);if(i>o)throw e("malformed ulid, timestamp too large");return i},r.detectPrng=d,r.factory=s,r.monotonicFactory=function(r){r||(r=d());var e=0,t=void 0;return function(n){if(isNaN(n)&&(n=Date.now()),n<=e){var o=t=u(t);return c(e,10)+o}e=n;var i=t=f(16,r);return c(n,10)+i}},r.ulid=h,Object.defineProperty(r,"__esModule",{value:!0})}));