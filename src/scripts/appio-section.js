window.loadPromise.then(() => {
    window.devicesInterval = setInterval(requestDevices, 3000)
    requestDevices()
    updateWidgetSettings()
})

function requestDevices() {
    fetch("http://127.0.0.1:8081/data/devices", {credentials: "include"}) // url replaced in deploy script
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error. Status: ${response.status}`)
            }
            return response.json()
        })
        .then(data => {
            const
                noDataNote = document.getElementById("appio-users-no-data"),
                noDataNoteNotifications = document.getElementById("appio-notifications-no-data"),
                table = document.getElementById("appio-users-data")

            if (data.length > 0) {
                const tbody = table.querySelector("tbody")
                tbody.replaceChildren()

                data.forEach(device => {
                    const
                        row = document.createElement("tr"),
                        nameTd = document.createElement("td"),
                        notificationsTd = document.createElement("td")

                    nameTd.textContent = device.marketing_name
                    nameTd.dataset.platform = device.platform
                    notificationsTd.textContent = device.notifications_enabled ? "Enabled" : "Disabled"
                    notificationsTd.classList.add(device.notifications_enabled ? "enabled" : "disabled")

                    row.appendChild(nameTd)
                    row.appendChild(notificationsTd)

                    tbody.appendChild(row)
                })

                noDataNote.style.display = "none"
                noDataNoteNotifications.style.display = "none"
                table.style.display = "table"
            } else {
                noDataNote.style.display = "block"
                noDataNoteNotifications.style.display = "block"
                table.style.display = "none"
            }
        })
        .catch(err => {
            // clearInterval(window.devicesInterval) // can't use to make sure polling restarts after internet disconnection
            console.error(err)
        })
}

function updateWidgetSettings() {
    const
        selectedWidgetTemplate = document.getElementById(`widget-${(window.settings && window.settings.widget && window.settings.widget.template && window.settings.widget.template.split(",")[0]) || "static"}`),
        selectedWidgetDataType = document.getElementById(`widget-data-${window.settings && window.settings.widget && window.settings.widget.source && window.settings.widget.source.type}`),
        widgetDataInput = document.querySelector("input[name=appio-widget-data-value]"),
        widgets = document.querySelectorAll(".widget-frame"),
        staticRadio = document.getElementById(`widget-data-static`)

    // Data
    widgetDataInput.addEventListener("click", () => {
        staticRadio.checked = true
        staticRadio.dispatchEvent(new Event("change"))
    })

    widgetDataInput.addEventListener("input", () => {
        staticRadio.dispatchEvent(new Event("change"))
    })

    document.querySelectorAll('input[name="appio-widget-data-type"]').forEach(radio => {
        radio.addEventListener("change", e => {
            const val = e.target.value === "static" ? widgetDataInput.value : "Rnd"
            widgets.forEach(el => {
                el.querySelector("var").innerText = val
            })
        })
    })

    // Data Init values
    widgetDataInput.value = (window.settings && window.settings.widget && window.settings.widget.source && window.settings.widget.source.data) || 0
    if (selectedWidgetTemplate) {
        selectedWidgetTemplate.checked = true
        selectedWidgetTemplate.dispatchEvent(new Event("change"))
    }
    if (selectedWidgetDataType) {
        selectedWidgetDataType.checked = true
        selectedWidgetDataType.dispatchEvent(new Event("change"))
    }

    // Design
    const
        textColorEl = document.querySelector("[name=appio-widget-text-color]"),
        backgroundColorEl = document.querySelector("[name=appio-widget-background-color]"),
        tintColorEl = document.querySelector("[name=appio-widget-tint-color]")

    textColorEl.addEventListener("change", e => {
        let color = e.target.value
        if (color === "primary") {
            color = "var(--ap-body-text-color)"
        }
        widgets.forEach(el => {
            el.style.color = color
        })
    })

    backgroundColorEl.addEventListener("change", e => {
        let color = e.target.value
        if (color === "primary") {
            color = "var(--ap-widget-frame-bg-color)"
        }
        widgets.forEach(el => {
            el.style.backgroundColor = color
        })
    })

    tintColorEl.addEventListener("change", e => {
        let color = e.target.value
        if (color === "primary") {
            color = "var(--color-gray1)"
        }
        widgets.forEach(el => {
            let elements = el.querySelectorAll("circle")
            if (elements) {
                elements.forEach(el2 => {
                    el2.style.stroke = color
                })
            }
        })
    })

    // Design Init values
    let color = window.settings && window.settings.widget && window.settings.widget.template && window.settings.widget.template.split(",")[1]
    if (color) {
        textColorEl.value = color
        textColorEl.dispatchEvent(new Event("change"))
    }

    let backgroundColor = window.settings && window.settings.widget && window.settings.widget.template && window.settings.widget.template.split(",")[2]
    if (backgroundColor) {
        backgroundColorEl.value = backgroundColor
        backgroundColorEl.dispatchEvent(new Event("change"))
    }

    let tintColor = window.settings && window.settings.widget && window.settings.widget.template && window.settings.widget.template.split(",")[3]
    if (tintColor) {
        tintColorEl.value = tintColor
        tintColorEl.dispatchEvent(new Event("change"))
    }
}

const notificationsForm = document.getElementById("appio-notifications-form")
notificationsForm.addEventListener("submit", event => {
    event.preventDefault()

    const
        button = notificationsForm.querySelector("button"),
        messageEl = notificationsForm.querySelector("textarea"),
        titleEl = notificationsForm.querySelector("input[name=title]"),
        linkEl = notificationsForm.querySelector("input[name=link]"),
        imageEl = notificationsForm.querySelector("input[name=image]"),
        sendAtEl = notificationsForm.querySelector("select[name=send-at]"),
        confirmation = document.getElementById("appio-notification-confirmation")

    button.disabled = true

    fetch("http://127.0.0.1:8081/data/notification", { // url replaced in deploy script
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            payload: {
                title: titleEl.value,
                message: messageEl.value,
                link: linkEl.value || "",
                image: imageEl.checked,
            },
            send_at: sendAtEl.value
        }),
        credentials: "include",
    })
        .then(response => {
            if (response.ok) {
                messageEl.value = ""
                // titleEl.value = "" // disabled while input is hidden
                linkEl.value = ""
                imageEl.checked = false
                confirmation.textContent = "Notification sent"
                confirmation.classList.remove("error")
                setTimeout(() => confirmation.textContent = "", 1000)
            } else {
                throw new Error("invalid response")
            }
        })
        .catch(err => {
            console.error(err)
            confirmation.textContent = "Error 😔"
            confirmation.classList.add("error")
            setTimeout(() => confirmation.textContent = "", 1000)
        })
        .finally(() => {
            button.disabled = false
        })
})

const widgetsForm = document.getElementById("appio-widgets-form")
widgetsForm.addEventListener("submit", event => {
    event.preventDefault()

    const
        widgetId = window.settings && window.settings.widget && window.settings.widget.id,
        confirmation = document.getElementById("appio-widgets-confirmation")

    function showError(err) {
        console.error(err)
        confirmation.textContent = "Error 😔"
        confirmation.classList.add("error")
        setTimeout(() => confirmation.textContent = "", 1000)
    }

    if (!widgetId) {
        showError("Widget ID not found")
        return
    }

    const
        button = widgetsForm.querySelector("button"),
        data = new FormData(event.target),
        widgetTemplate = data.get("appio-widget-template"),
        widgetData = Number(data.get("appio-widget-data-value")),
        widgetType = data.get("appio-widget-data-type"),
        textColor = data.get("appio-widget-text-color"),
        backgroundColor = data.get("appio-widget-background-color"),
        tintColor = data.get("appio-widget-tint-color")

    button.disabled = true

    fetch(`http://127.0.0.1:8081/data/widget/${widgetId}`, { // url replaced in deploy script
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            template: [widgetTemplate, textColor, backgroundColor, tintColor].join(","),
            data: widgetData,
            type: widgetType,
        }),
        credentials: "include",
    })
        .then(response => {
            if (response.ok) {
                confirmation.textContent = "Widgets updated"
                confirmation.classList.remove("error")
                setTimeout(() => confirmation.textContent = "", 1000)
            } else {
                throw new Error("invalid response")
            }
        })
        .catch(err => {
            showError(err)
        })
        .finally(() => {
            button.disabled = false

            document.querySelectorAll(".widget-frame var").forEach(el => {
                el.innerText = widgetData
                el.innerText = widgetType === "random" ? "Rnd" : widgetData
            })
        })
})