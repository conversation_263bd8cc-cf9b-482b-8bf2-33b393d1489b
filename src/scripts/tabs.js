// Record tab changes
const radios = document.querySelectorAll("input[name=browser-tabs], input[name=appio-tabs]")
radios.forEach(radio => {
    radio.addEventListener("change", event => {
        // URL update
        const
            browserTabEl = document.querySelector("input[name=browser-tabs]:checked"),
            browserTab = browserTabEl && browserTabEl.id,
            appioTabEl = document.querySelector("input[name=appio-tabs]:checked"),
            appioTab = appioTabEl && appioTabEl.id

        // Update URL only if initialised by user
        if (event.isTrusted) {
            let hash = `#${browserTab}`
            if (browserTab === "appio") {
                hash += `/${appioTab}`
            }
            history.pushState(null, "", hash)
        }

        document.dispatchEvent(new CustomEvent("tab-change", { detail: browserTab }))
    })
})

// Select active tabs based on the url
function setActiveTabs() {
    const
        hashParts = location.hash.split("#"),
        tabPath = (hashParts[1] && hashParts[1].split("/")) || null

    if (tabPath === null) {
        const radio = document.getElementById("app")
        radio.checked = true
        radio.dispatchEvent(new Event("change"))
        return
    }

    tabPath.forEach(path => {
        const radio = document.getElementById(path)
        if (radio) {
            radio.checked = true
            radio.dispatchEvent(new Event("change"))
        }
    })

    // Scroll to browser
    const
        browser = document.getElementById("browser"),
        offset = 20,
        y = browser.getBoundingClientRect().top + window.scrollY - offset
    window.scrollTo(0, y)
}
setActiveTabs()

// Browser back button
window.addEventListener("hashchange", setActiveTabs)
