function qst(name) {
    const params = new URLSearchParams(window.location.search)
    return params.get(name)
}

// Check for id in the url without leading slash
let serviceId
if (qst("svc")) {
    if (/^demo_svc_[0-9a-hjkmnp-tv-z]{26}$/.test(qst("svc"))) {
        serviceId = qst("svc")
        console.log("Service ID overwrite:", serviceId)
        setServiceId(serviceId)
    } else {
        console.error(`Invalid service ID format. Expected: "demo_svc_" followed by 26 alpha characters.`)
    }
}

function setServiceId(serviceId) {
    document.cookie = `service_id=${serviceId}; path=/; max-age=604800; domain=demo.appio.so; SameSite=Strict; Secure`
    console.log("Service ID:", serviceId)
}

// Fetch settings
window.settings = {}
window.loadPromise = fetch(`http://127.0.0.1:8081/data/settings`, {credentials: "include"}) // url replaced in deploy script
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error. Status: ${response.status}`)
        }
        return response.json()
    })
    .then(data => {
        window.settings = data

        console.log("Init Appio with service ID:", data.service && data.service.id)

        /* eslint-disable-next-line no-undef */
        window.appio = Appio(data.service && data.service.id)

        styleApp(data.service)
    })
    .catch(err => {
        deleteCookie()
        showError()
        console.error(err)
    })

function showError() {
    const div = document.createElement("div"),
        span = document.createElement("span")

    span.innerHTML = `Failed to fetch data.<br> <a href="/">Please try again</a>`
    Object.assign(div.style, {
        display: "grid",
        placeItems: "center",
        textAlign: "center",
        position: "fixed",
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: "var(--ap-body-background-color)",
        fontSize: "2em",
        zIndex: 9999
    })

    div.appendChild(span)
    document.body.appendChild(div)
}

/* eslint-disable-next-line no-unused-vars */
function clickOpenApp() {
    window.appio.open({
        /* eslint-disable-next-line no-undef */
        user: `${ULID.ulid()}:<EMAIL>`
    })
}

function deleteCookie() {
    document.cookie = "service_id=; path=/; max-age=0; domain=demo.appio.so; SameSite=Strict; Secure"
}

function styleApp(service) {
    if (!service) {
        return
    }

    // titles
    document.querySelectorAll("[data-replace=service-title]").forEach(el => el.textContent = service.title)
    document.title = `Appio + ${service.title}`
    // tab icon
    document.querySelector("[for=app]").style.setProperty("--app-icon", `url(${service.logo_url})`)
    document.getElementById("favicon").href = service.logo_url
    // profile icon
    document.getElementById("app-profile-logo").setAttribute("href", service.logo_url)
}

function checkUrlToClearData() {
    if (qst("clear") !== null) {
        deleteCookie()

        // Refresh the page without a clear flag
        const url = new URL(window.location.href)
        url.searchParams.delete("clear")
        window.location.href = url.href
    }
}
checkUrlToClearData()

document.addEventListener("DOMContentLoaded", () => {
    window.loadPromise.then(() => {
        // Share session
        const sessionBtnEl = document.getElementById("session-btn")
        sessionBtnEl && sessionBtnEl.addEventListener("click", (e) => {
            e.preventDefault()

            const
                serviceId = (window.settings && window.settings.service && window.settings.service.id) || null,
                url = location.origin + "/?svc=" + serviceId + location.hash

            if (serviceId === null) {
                console.error("Service ID not found")
                return
            }

            history.pushState(null, "", url)

            if (navigator.clipboard) {
                navigator.clipboard.writeText(url)

                let el = e.currentTarget
                // Special empty space to supplement missing horizontal padding: https://www.compart.com/en/unicode/U+2800
                el.dataset.confirmation = "⠀URL copied to clipboard⠀"
                setTimeout(() => {
                    el.dataset.confirmation = ""
                }, 1000)
            }
        })

        // Install code
        const
            key = (window.settings && window.settings.key) || null,
            serviceId = (window.settings && window.settings.service && window.settings.service.id) || null
        if (key) {
            let installCodeBtn = document.getElementById("install-code-btn")
            installCodeBtn.href = installCodeBtn.href.replace("/#", `/?key=${key}&svc=${serviceId}#`)
        }
    })
})