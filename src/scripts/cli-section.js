const
    cli = document.querySelector("[data-tab='cli']"),
    cliInput = document.getElementById("cli-input"),
    cliOutput = document.getElementById("cli-output"),
    cliPrefix = document.getElementById("cli-prefix").textContent,
    cliHistory = [],
    cliClickCommands = {
        1: `curl -X POST https://api.appio.so/v1/notifications \\\n-H "Authorization: Bearer API_KEY" \\\n-H "Content-Type: application/json" \\\n-d '{"payload":{"title":"Appio Demo CLI","message":"Hello from Appio Demo CLI","link":"https://demo.appio.so"}}'`,
        2: `curl -X GET https://api.appio.so/v1/devices \\\n-H "Authorization: Bearer API_KEY"`,
    },
    cliCommands = {
        "sample": () => { return [
            "Welcome to Appio CLI simulator.",
            `Docs: <a href="https://docs.appio.so/" target="_blank">docs.appio.so</a>`,
            "Sample commands:",
            "",
            `[1] <a href="#[1]" onclick="processCliCommand(addKeyToCliCommand(cliClickCommands[1]));return false">Send notification via API</a>`,
            `[2] <a href="#[2]" onclick="processCliCommand(addKeyToCliCommand(cliClickCommands[2]));return false">List connected users/devices via API</a>`,
            `[3] <a href="#[3]" onclick="processCliCommand('help');return false">Help</a>`,
            " ",
        ].join("\n")
        },
        "help": () => { return [
            "Available commands:",
            "",
            "clear     clear screen",
            "curl      a tool for testing Appio's API",
            "docs      API documentation",
            "help, ?   print this message",
            "key       print your API key",
            "sample    print sample commands",
            "service   print service ID",
            " ",
        ].join("\n")
        },
        "curl": params => {
            // renders async, shows response with delay
            curlRequest(params)
            return ""
        },
        "clear": () => {
            cliClearScreen()
            return ""
        },
        "docs": () => {
            return `<a href="https://docs.appio.so/" target="_blank">https://docs.appio.so/</a>`
        },
        "key": () => {
            return (window.settings && window.settings.key) || "n/a"
        },
        "service": () => {
            return (window.settings && window.settings.service && window.settings.service.id) || "n/a"
        },
        "1": () => {
            processCliCommand(addKeyToCliCommand(cliClickCommands[1]))
        },
        "2": () => {
            processCliCommand(addKeyToCliCommand(cliClickCommands[2]))
        },
        "3": () => {
            processCliCommand(`help`)
        },
    }

let cliHistoryPointer = 0

// Init message
render(cliCommands["sample"]())

// Cli aliases
cliCommands["?"] = cliCommands["man"] = cliCommands["help"]
cliCommands["[1]"] = cliCommands["1"]
cliCommands["[2]"] = cliCommands["2"]
cliCommands["[3]"] = cliCommands["3"]

document.addEventListener("tab-change", event => {
    if (event.detail !== "cli") {
        return
    }
    cliFocusInput()
})

document.querySelector("[for=cli]").addEventListener("click", cliFocusInput)

function cliFocusInput() {
    if (! isSmallScreen()) {
        cliInput.focus()
    }
    // Scroll the terminal window content to the bottom to show focused input
    cli.scrollTo(0, cli.scrollHeight)
}

cliInput.addEventListener("keydown", event => {
    if (event.key === "Enter") {
        processCliCommand(cliInput.value)
        cliInput.value = ""
    }
    if (event.key === "ArrowUp") {
        showHistory("up")
    }
    if (event.key === "ArrowDown") {
        showHistory("down")
    }
    if (event.ctrlKey && event.key === "c") {
        cliInput.value = ""
    }
    if (event.ctrlKey && event.key === "l") {
        cliClearScreen()
    }
    if (event.metaKey && event.key === "k") {
        cliClearScreen()
    }
})

function cliClearScreen() {
    cliOutput.innerHTML = ""
}

function processCliCommand(input) {
    const [cmd, params] = input.split(/(?<=^\S+)\s/)

    // Don't process or add empty commands to history
    if (cmd.trim() === "") {
        render(`${cliPrefix}`)
        return
    }

    addHistory(input)

    if (cliCommands[cmd.toLowerCase()]) {
        // Output command
        render(`${cliPrefix} ${input}`, "cli-cmd-ok")
        render(cliCommands[cmd.toLowerCase()](params))
    } else {
        // Output command
        render(`${cliPrefix} ${input}`, "cli-cmd-err")
        render(`Command not found. Try typing <a href="#help" onclick="processCliCommand('help');return false">help</a>.`)
    }
}

function render(html, cssClass) {
    if (! html) {
        return
    }

    const line = document.createElement("div")
    line.innerHTML = html
    if (cssClass) {
        line.classList.add(cssClass)
    }
    cliOutput.append(line)
    cliFocusInput()
}

function addHistory(cmd) {
    // Strip out CLI new line `\` used in sample codes
    cliHistory.push(cmd.replace(/\\/g, ""))
    cliHistoryPointer = 0
}

function showHistory(dir) {
    const max = cliHistory.length
    if (dir === "up") {
        cliHistoryPointer = Math.min(cliHistoryPointer+1, max)
    }
    if (dir === "down") {
        cliHistoryPointer = Math.max(cliHistoryPointer-1, 0)
    }
    cliInput.value = cliHistory[max - cliHistoryPointer] || ""

    // Move caret to the end
    setTimeout(() => {
        cliInput.setSelectionRange(cliInput.value.length, cliInput.value.length)
    }, 1)
}

function isSmallScreen() {
    if (! window.matchMedia) {
        return false
    }

    const mediaQuery = window.matchMedia("(max-width: 768px), (max-height: 768px)")
    return mediaQuery.matches
}

function addKeyToCliCommand(command) {
    const key = window.settings && window.settings.key
    if (key) {
        return command.replace("API_KEY", key)
    }
    return command
}

function curlRequest(params) {
    startCliLoadingAnimation()
    fetch("http://127.0.0.1:8081/data/cli", { // url replaced in deploy script
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({ cmd: params }),
        credentials: "include",
    })
        .then(response => response.json())
        .then(data => {
            render(JSON.stringify(data, null, 2))
        })
        .catch(err => {
            console.error(err)
            render("curl: (6) Could not resolve host: api.appio.so")
        })
        .finally(() => {
            endCliLoadingAnimation()
        })
}

let cliAnimationTimer
function startCliLoadingAnimation() {
    cliInput.disabled = true
    let i = 0
    cliAnimationTimer = setInterval(() => {
        cliInput.placeholder = "Loading" + ".".repeat(i)
        i = ++i % 4
    }, 200)
}

function endCliLoadingAnimation() {
    clearTimeout(cliAnimationTimer)
    cliInput.placeholder = ""
    cliInput.disabled = false
    cliFocusInput()
}
